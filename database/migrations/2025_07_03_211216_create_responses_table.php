<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('responses', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('prompt_id')->constrained()->onDelete('cascade');
            $table->string('model'); // Nome do modelo usado (ex: llama2, codellama, etc)
            $table->longText('content'); // Resposta do modelo
            $table->json('metadata')->nullable(); // Informações adicionais (tempo de processamento, tokens, etc)
            $table->string('status')->default('completed'); // completed, failed
            $table->timestamp('generated_at');
            $table->timestamps();

            $table->index(['prompt_id', 'model']);
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('responses');
    }
};
