<?php

namespace App\Services\AI;

class AIService
{
    /**
     * Instância do serviço de IA.
     *
     * @var AIServiceInterface
     */
    protected AIServiceInterface $service;

    /**
     * Construtor.
     *
     * @param AIServiceInterface|null $service
     */
    public function __construct(?AIServiceInterface $service = null)
    {
        $this->service = $service ?? AIServiceFactory::createDefault();
    }

    /**
     * Define o serviço de IA a ser usado.
     *
     * @param AIServiceInterface $service
     * @return self
     */
    public function setService(AIServiceInterface $service): self
    {
        $this->service = $service;
        return $this;
    }

    /**
     * Obtém o serviço de IA atual.
     *
     * @return AIServiceInterface
     */
    public function getService(): AIServiceInterface
    {
        return $this->service;
    }

    /**
     * Cria uma nova instância com um serviço específico.
     *
     * @param string $type
     * @param array $config
     * @return self
     */
    public function withService(string $type, array $config = []): self
    {
        // Garantir que as configurações do arquivo config/ai.php sejam usadas
        $aiConfig = config('ai');
        $serviceType = strtolower($type);

        // Obter configurações específicas do serviço
        $serviceConfig = isset($aiConfig[$serviceType]) ? $aiConfig[$serviceType] : [];

        // Mesclar com as configurações fornecidas
        $mergedConfig = array_merge($serviceConfig, $config);

        $service = AIServiceFactory::create($type, $mergedConfig);
        return new self($service);
    }

    /**
     * Envia uma mensagem para o modelo de IA.
     *
     * @param string $prompt
     * @param array $options
     * @return string
     */
    public function sendMessage(string $prompt, array $options = []): string
    {
        return $this->service->sendMessage($prompt, $options);
    }

    /**
     * Envia uma conversa para o modelo de IA.
     *
     * @param array $messages
     * @param array $options
     * @return string
     */
    public function sendConversation(array $messages, array $options = []): string
    {
        return $this->service->sendConversation($messages, $options);
    }

    /**
     * Gera embeddings para um texto.
     *
     * @param string $text
     * @return array
     */
    public function generateEmbeddings(string $text): array
    {
        return $this->service->generateEmbeddings($text);
    }
}
