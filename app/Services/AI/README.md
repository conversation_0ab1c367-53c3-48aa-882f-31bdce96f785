# Abstração de Serviços de IA

Esta abstração permite usar diferentes serviços de IA (OpenAI, Google AI, OpenRouter, Ollama) de forma unificada.

## Configuração

### 1. <PERSON><PERSON><PERSON><PERSON> as variá<PERSON><PERSON> de ambiente

Adicione as se<PERSON><PERSON> variáveis ao seu arquivo `.env`:

```
# Serviço padrão a ser usado
AI_SERVICE_TYPE=ollama

# Configurações para Ollama
OLLAMA_API_URL=http://localhost:11434/api
OLLAMA_DEFAULT_MODEL=llama2
```

### 2. Publicar o arquivo de configuração (opcional)

```bash
php artisan vendor:publish --tag=ai-config
```

## Uso Básico

### Usando o serviço padrão

```php
$aiService = app('ai.service');
$response = $aiService->sendMessage('Olá, como você está?');
echo $response;
```

### Usando um serviço específico

```php
// Via factory
$openaiService = app(App\Services\AI\AIServiceFactory::class)->create('openai');
$response = $openaiService->sendMessage('Olá, como você está?');

// Via método withService
$aiService = app('ai.service');
$response = $aiService->withService('google')->sendMessage('Olá, como você está?');
```

### Enviando uma conversa completa

```php
$aiService = app('ai.service');
$messages = [
    ['role' => 'system', 'content' => 'Você é um assistente útil e amigável.'],
    ['role' => 'user', 'content' => 'Olá, como você está?'],
    ['role' => 'assistant', 'content' => 'Estou bem, obrigado por perguntar! Como posso ajudar você hoje?'],
    ['role' => 'user', 'content' => 'Pode me explicar o que é inteligência artificial?'],
];
$response = $aiService->sendConversation($messages);
```

### Gerando embeddings

```php
$aiService = app('ai.service');
$text = 'Este é um exemplo de texto para gerar embeddings.';
$embeddings = $aiService->generateEmbeddings($text);
```

### Usando opções personalizadas

```php
$aiService = app('ai.service');
$response = $aiService->sendMessage('Olá, como você está?', [
    'temperature' => 0.9,
    'max_tokens' => 500,
]);
```

## Recursos específicos do Ollama

### Usando o endpoint de completions

```php
$ollamaService = app(App\Services\AI\AIServiceFactory::class)->create('ollama');
if ($ollamaService instanceof App\Services\AI\OllamaService) {
    $response = $ollamaService->generateCompletion('Escreva um poema sobre inteligência artificial');
    echo $response;
}
```

## Adicionando novos serviços

Para adicionar um novo serviço:

1. Crie uma nova classe que estenda `BaseAIService` e implemente `AIServiceInterface`
2. Adicione o novo serviço à factory em `AIServiceFactory::create()`
3. Adicione as configurações do novo serviço ao arquivo `config/ai.php`
