<?php

namespace App\Services\AI;

use InvalidArgumentException;

class AIServiceFactory
{
    /**
     * Cria uma instância do serviço de IA com base no tipo especificado.
     *
     * @param string $type Tipo de serviço ('openai', 'google', 'openrouter')
     * @param array $config Configurações adicionais
     * @return AIServiceInterface
     * @throws InvalidArgumentException
     */
    public static function create(string $type, array $config = []): AIServiceInterface
    {
        // Garantir que as configurações do arquivo config/ai.php sejam usadas
        $aiConfig = config('ai');

        // Mesclar configurações específicas do serviço com as configurações fornecidas
        $serviceType = strtolower($type);
        $serviceConfig = isset($aiConfig[$serviceType]) ? $aiConfig[$serviceType] : [];
        $mergedConfig = array_merge($serviceConfig, $config);

        return match ($serviceType) {
            'ollama' => new OllamaService($mergedConfig),
            default => throw new InvalidArgumentException("Unsupported AI service type: {$type}"),
        };
    }

    /**
     * Cria uma instância do serviço de IA com base na configuração.
     *
     * @param array $config Configurações
     * @return AIServiceInterface
     */
    public static function createFromConfig(array $config = []): AIServiceInterface
    {
        $type = $config['type'] ?? env('AI_SERVICE_TYPE', 'openai');
        return self::create($type, $config);
    }

    /**
     * Cria uma instância do serviço de IA padrão com base na configuração do ambiente.
     *
     * @return AIServiceInterface
     */
    public static function createDefault(): AIServiceInterface
    {
        $type = env('AI_SERVICE_TYPE', 'openai');
        return self::create($type);
    }
}
