<?php

namespace App\Providers;

use App\Services\AI\AIService;
use App\Services\AI\AIServiceFactory;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Registrar o AI Service
        $this->app->singleton('ai.service', function ($app) {
            return new AIService(AIServiceFactory::createDefault());
        });

        $this->app->alias('ai.service', AIService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
