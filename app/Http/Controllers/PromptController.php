<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePromptRequest;
use App\Models\Prompt;
use App\Jobs\ProcessPromptJob;
use Illuminate\Http\JsonResponse;

class PromptController extends Controller
{
    /**
     * Store a new prompt
     */
    public function store(StorePromptRequest $request): JsonResponse
    {
        $prompt = Prompt::create([
            'content' => $request->content,
            'metadata' => $request->metadata ?? [],
        ]);

        // Processar arquivos anexados se existirem
        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $prompt->addMediaFromRequest('attachments')
                    ->usingFileName($file->getClientOriginalName())
                    ->toMediaCollection('attachments');
            }
        }

        // Determinar quais modelos usar
        $models = $request->models ?? ['llama2']; // modelo padrão

        // Disparar jobs para cada modelo
        foreach ($models as $model) {
            ProcessPromptJob::dispatch($prompt, $model);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'status' => $prompt->status,
                'models' => $models,
                'created_at' => $prompt->created_at,
            ],
            'message' => 'Prompt criado e enviado para processamento.',
        ], 201);
    }

    /**
     * Show prompt details
     */
    public function show(Prompt $prompt): JsonResponse
    {
        $prompt->load(['responses', 'media']);

        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'content' => $prompt->content,
                'status' => $prompt->status,
                'metadata' => $prompt->metadata,
                'attachments' => $prompt->getMedia('attachments')->map(function ($media) {
                    return [
                        'id' => $media->id,
                        'name' => $media->name,
                        'file_name' => $media->file_name,
                        'mime_type' => $media->mime_type,
                        'size' => $media->size,
                        'url' => $media->getUrl(),
                    ];
                }),
                'responses' => $prompt->responses->map(function ($response) {
                    return [
                        'uuid' => $response->uuid,
                        'model' => $response->model,
                        'content' => $response->content,
                        'status' => $response->status,
                        'metadata' => $response->metadata,
                        'generated_at' => $response->generated_at,
                    ];
                }),
                'created_at' => $prompt->created_at,
                'processed_at' => $prompt->processed_at,
            ],
        ]);
    }

    /**
     * Get prompt status
     */
    public function status(Prompt $prompt): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'uuid' => $prompt->uuid,
                'status' => $prompt->status,
                'responses_count' => $prompt->responses()->count(),
                'processed_at' => $prompt->processed_at,
            ],
        ]);
    }

    /**
     * Get prompt responses
     */
    public function responses(Prompt $prompt): JsonResponse
    {
        $responses = $prompt->responses()->get();

        return response()->json([
            'success' => true,
            'data' => $responses->map(function ($response) {
                return [
                    'uuid' => $response->uuid,
                    'model' => $response->model,
                    'content' => $response->content,
                    'status' => $response->status,
                    'metadata' => $response->metadata,
                    'generated_at' => $response->generated_at,
                ];
            }),
        ]);
    }
}
