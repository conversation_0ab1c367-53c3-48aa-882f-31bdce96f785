<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePromptRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'content' => 'required|string|max:10000',
            'models' => 'sometimes|array',
            'models.*' => 'string|max:100',
            'attachments' => 'sometimes|array|max:5',
            'attachments.*' => 'file|max:10240|mimes:jpeg,png,gif,pdf,txt,doc,docx',
            'metadata' => 'sometimes|array',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'content.required' => 'O conteúdo do prompt é obrigatório.',
            'content.max' => 'O conteúdo do prompt não pode exceder 10.000 caracteres.',
            'attachments.max' => 'Máximo de 5 arquivos permitidos.',
            'attachments.*.max' => 'Cada arquivo deve ter no máximo 10MB.',
            'attachments.*.mimes' => 'Tipos de arquivo permitidos: jpeg, png, gif, pdf, txt, doc, docx.',
        ];
    }
}
