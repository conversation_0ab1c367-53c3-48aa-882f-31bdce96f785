<?php

namespace App\Jobs;

use App\Models\Prompt;
use App\Models\Response;
use App\Services\AI\AIService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessPromptJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $tries = 3;
    public $timeout = 300; // 5 minutos

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Prompt $prompt,
        public string $model
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(AIService $aiService): void
    {
        try {
            Log::info("Processando prompt {$this->prompt->uuid} com modelo {$this->model}");

            // Atualizar status do prompt para processing se ainda estiver pending
            if ($this->prompt->status === 'pending') {
                $this->prompt->update(['status' => 'processing']);
            }

            // Preparar o conteúdo do prompt incluindo informações dos arquivos
            $promptContent = $this->preparePromptContent();

            // Configurar o AI Service para usar o modelo específico
            $aiServiceWithModel = $aiService->withService('ollama', [
                'model' => $this->model
            ]);

            // Fazer a requisição para o AI Service
            $startTime = microtime(true);
            $responseContent = $aiServiceWithModel->sendMessage($promptContent);
            $endTime = microtime(true);

            // Criar a resposta no banco de dados
            Response::create([
                'prompt_id' => $this->prompt->id,
                'model' => $this->model,
                'content' => $responseContent,
                'metadata' => [
                    'processing_time' => $endTime - $startTime,
                    'service_type' => 'ollama',
                ],
                'status' => 'completed',
                'generated_at' => now(),
            ]);

            // Verificar se todas as respostas foram geradas
            $this->checkPromptCompletion();

            Log::info("Prompt {$this->prompt->uuid} processado com sucesso para modelo {$this->model}");

        } catch (Exception $e) {
            Log::error("Erro ao processar prompt {$this->prompt->uuid} com modelo {$this->model}: " . $e->getMessage());

            // Criar resposta de erro apenas na última tentativa
            if ($this->attempts() >= $this->tries) {
                Response::create([
                    'prompt_id' => $this->prompt->id,
                    'model' => $this->model,
                    'content' => 'Erro ao processar o prompt: ' . $e->getMessage(),
                    'metadata' => [
                        'error' => true,
                        'error_message' => $e->getMessage(),
                        'attempts' => $this->attempts(),
                        'max_tries' => $this->tries,
                    ],
                    'status' => 'failed',
                    'generated_at' => now(),
                ]);

                // Verificar o status final do prompt
                $this->checkPromptCompletion();
            } else {
                Log::info("Tentativa {$this->attempts()}/{$this->tries} falhou para prompt {$this->prompt->uuid} com modelo {$this->model}. Tentando novamente...");
            }

            throw $e;
        }
    }

    /**
     * Preparar o conteúdo do prompt incluindo informações dos arquivos
     */
    private function preparePromptContent(): string
    {
        $content = $this->prompt->content;

        // Adicionar informações sobre arquivos anexados
        $attachments = $this->prompt->getMedia('attachments');
        if ($attachments->count() > 0) {
            $content .= "\n\nArquivos anexados:\n";
            foreach ($attachments as $attachment) {
                $content .= "- {$attachment->name} ({$attachment->mime_type})\n";

                // Se for um arquivo de texto, incluir o conteúdo
                if (str_starts_with($attachment->mime_type, 'text/')) {
                    try {
                        $fileContent = file_get_contents($attachment->getPath());
                        $content .= "Conteúdo do arquivo {$attachment->name}:\n```\n{$fileContent}\n```\n\n";
                    } catch (Exception $e) {
                        Log::warning("Não foi possível ler o arquivo {$attachment->name}: " . $e->getMessage());
                    }
                }
            }
        }

        return $content;
    }

    /**
     * Verificar se o prompt foi completamente processado
     */
    private function checkPromptCompletion(): void
    {
        // Recarregar o prompt para ter os dados mais atuais
        $this->prompt->refresh();

        // Contar respostas por status
        $responses = $this->prompt->responses;
        $totalResponses = $responses->count();
        $completedResponses = $responses->where('status', 'completed')->count();
        $failedResponses = $responses->where('status', 'failed')->count();

        // Se não há respostas ainda, manter como processing
        if ($totalResponses === 0) {
            return;
        }

        // Se todas as respostas falharam, marcar prompt como failed
        if ($totalResponses === $failedResponses) {
            $this->prompt->update([
                'status' => 'failed',
                'processed_at' => now(),
            ]);
            return;
        }

        // Se há pelo menos uma resposta bem-sucedida, marcar como completed
        // (mesmo que algumas tenham falhado)
        if ($completedResponses > 0) {
            $this->prompt->update([
                'status' => 'completed',
                'processed_at' => now(),
            ]);
            return;
        }

        // Se chegou aqui, ainda há respostas sendo processadas
        // Manter o status atual (processing)
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("Job falhou definitivamente para prompt {$this->prompt->uuid} com modelo {$this->model}: " . $exception->getMessage());

        // Criar resposta de erro se ainda não foi criada
        $existingResponse = Response::where('prompt_id', $this->prompt->id)
            ->where('model', $this->model)
            ->first();

        if (!$existingResponse) {
            Response::create([
                'prompt_id' => $this->prompt->id,
                'model' => $this->model,
                'content' => 'Erro ao processar o prompt: ' . $exception->getMessage(),
                'metadata' => [
                    'error' => true,
                    'error_message' => $exception->getMessage(),
                    'attempts' => $this->tries,
                    'max_tries' => $this->tries,
                    'failed_definitively' => true,
                ],
                'status' => 'failed',
                'generated_at' => now(),
            ]);
        }

        // Verificar o status final do prompt
        $this->checkPromptCompletion();
    }
}
