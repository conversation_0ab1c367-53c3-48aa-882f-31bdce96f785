<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Response extends Model
{
    protected $fillable = [
        'uuid',
        'prompt_id',
        'model',
        'content',
        'metadata',
        'status',
        'generated_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'generated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($response) {
            if (empty($response->uuid)) {
                $response->uuid = (string) Str::uuid();
            }
        });
    }

    public function prompt(): BelongsTo
    {
        return $this->belongsTo(Prompt::class);
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }
}
