<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PromptController;
use App\Http\Controllers\SystemController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Rotas do sistema
Route::prefix('system')->group(function () {
    Route::get('/status', [SystemController::class, 'status'])->name('system.status');
    Route::get('/models', [SystemController::class, 'models'])->name('system.models');
    Route::get('/stats', [SystemController::class, 'stats'])->name('system.stats');
});

// Rotas para o sistema de prompts
Route::prefix('prompts')->group(function () {
    Route::post('/', [PromptController::class, 'store'])->name('prompts.store');
    Route::get('/{prompt}', [PromptController::class, 'show'])->name('prompts.show');
    Route::get('/{prompt}/status', [PromptController::class, 'status'])->name('prompts.status');
    Route::get('/{prompt}/responses', [PromptController::class, 'responses'])->name('prompts.responses');
});
