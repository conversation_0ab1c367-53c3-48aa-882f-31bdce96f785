<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default AI Service
    |--------------------------------------------------------------------------
    |
    | This option controls the default AI service that will be used by your
    | application. You may set this to any of the connections defined in the
    | "services" array below.
    |
    */

    'default' => env('AI_SERVICE_TYPE', 'ollama'),

    /*
    |--------------------------------------------------------------------------
    | AI Services
    |--------------------------------------------------------------------------
    |
    | Here you may configure the AI services for your application. Each service
    | has its own configuration options.
    |
    */

    'ollama' => [
        'api_url' => env('OLLAMA_API_URL', 'http://localhost:11434/api'),
        'model' => env('OLLAMA_DEFAULT_MODEL', 'llama2'),
        'timeout' => env('OLLAMA_TIMEOUT', 120),
        'temperature' => 0.7,
        'num_predict' => 1000,
        'top_k' => 40,
        'top_p' => 0.9,
        'stream' => false,
    ],

    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'model' => env('OPENAI_DEFAULT_MODEL', 'gpt-3.5-turbo'),
        'temperature' => 0.7,
        'max_tokens' => 1000,
    ],

    'google' => [
        'api_key' => env('GOOGLE_AI_API_KEY'),
        'model' => env('GOOGLE_AI_DEFAULT_MODEL', 'gemini-pro'),
        'temperature' => 0.7,
        'max_tokens' => 1000,
    ],

    'openrouter' => [
        'api_key' => env('OPENROUTER_API_KEY'),
        'model' => env('OPENROUTER_DEFAULT_MODEL', 'openai/gpt-3.5-turbo'),
        'temperature' => 0.7,
        'max_tokens' => 1000,
    ],
];
